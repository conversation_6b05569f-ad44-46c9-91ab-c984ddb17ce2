<script setup lang="ts"></script>

<template>
  <!-- Trigger hot reload -->
  <div class="min-h-screen bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
      <h1 class="text-3xl font-bold text-blue-600 mb-4">You did it!</h1>
      <p class="text-gray-700 mb-4">
        Tailwind CSS is now working with your Vite + Vue project! 🎉
      </p>
      <p class="text-gray-600">
        Visit 
        <a href="https://vuejs.org/" target="_blank" rel="noopener" 
           class="text-blue-500 hover:text-blue-700 font-medium">
          vuejs.org
        </a> 
        to read the documentation
      </p>
      <div class="mt-6 p-4 bg-blue-50 rounded-md">
        <p class="text-blue-800">
          This is a test box with Tailwind's utility classes. If you see this styled correctly, Tailwind is working! 🚀
        </p>
      </div>
    </div>
  </div>
</template>
